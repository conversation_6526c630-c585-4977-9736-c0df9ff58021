#!/usr/bin/env python3
"""
Bot Binomo - Estratégia Otimizada v2.0
Implementa melhorias baseadas nos testes com dados em tempo real
"""

import telebot
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import time
import os
from dotenv import load_dotenv
import logging
from dataclasses import dataclass
from binomo_realtime_provider import BinomoRealtimeProvider
import json
from typing import Optional, Dict, Tuple

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

@dataclass
class TradeResult:
    entry_time: datetime
    entry_price: float
    operation: str
    probability: float
    confidence_level: str
    expiration_time: datetime = None
    exit_price: float = None
    result: str = None

class OptimizedTradingBot:
    def __init__(self):
        print("🚀 Inicializando Bot com Estratégia Otimizada v2.0...")
        self._load_config()
        self.bot = telebot.TeleBot(self.config['telegram_token'])
        self._setup_telegram_handlers()
        self.scaler = StandardScaler()
        self.active_trade = None
        self.processing_signal = False
        
        # Histórico para análise adaptativa
        self.signal_history = []
        self.performance_metrics = {
            'total_signals': 0,
            'wins': 0,
            'losses': 0,
            'win_rate': 0.0
        }
        
        # Inicializar BinomoRealtimeProvider
        self.data_provider = BinomoRealtimeProvider(self.config)
        print("✅ Bot Otimizado inicializado com sucesso!")

    def _load_config(self):
        """Carrega configurações otimizadas"""
        load_dotenv()
        
        self.config = {
            'telegram_token': os.getenv('TELEGRAM_TOKEN'),
            'chat_id': os.getenv('TELEGRAM_CHAT_ID'),
            'min_confidence': float(os.getenv('MIN_CONFIDENCE', '60.0'))  # Reduzido para mais sinais
        }
        
        try:
            with open('config_binomo.json', 'r') as f:
                binomo_config = json.load(f)
                self.config.update(binomo_config)
        except FileNotFoundError:
            self.config.update({
                'SYMBOL': 'Z-CRY/IDX',
                'TIMEFRAME': '1',
                'USE_REALTIME_API': True
            })

    def prepare_features_optimized(self, df):
        """Features otimizadas com melhor precisão"""
        try:
            if len(df) < 20:
                return None

            df = df.copy()
            
            # === MÉDIAS MÓVEIS OTIMIZADAS ===
            df['sma_5'] = df['close'].rolling(window=5, min_periods=1).mean()
            df['sma_10'] = df['close'].rolling(window=10, min_periods=1).mean()
            df['sma_20'] = df['close'].rolling(window=20, min_periods=1).mean()
            df['ema_8'] = df['close'].ewm(span=8).mean()
            df['ema_21'] = df['close'].ewm(span=21).mean()
            
            # === RSI OTIMIZADO ===
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14, min_periods=1).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14, min_periods=1).mean()
            rs = gain / (loss + 1e-10)  # Evitar divisão por zero
            df['rsi'] = 100 - (100 / (1 + rs))
            df['rsi_smooth'] = df['rsi'].rolling(window=3).mean()  # RSI suavizado
            
            # === MACD MELHORADO ===
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            df['macd_momentum'] = df['macd_histogram'].diff()  # Momentum do MACD
            
            # === BOLLINGER BANDS AVANÇADO ===
            df['bb_middle'] = df['close'].rolling(window=20, min_periods=1).mean()
            bb_std = df['close'].rolling(window=20, min_periods=1).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            df['bb_squeeze'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']  # Squeeze indicator
            
            # === VOLUME INTELIGENTE ===
            df['volume_sma'] = df['tick_volume'].rolling(window=10, min_periods=1).mean()
            df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
            df['volume_trend'] = df['tick_volume'].rolling(window=5).apply(
                lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1, raw=False
            )
            df['volume_acceleration'] = df['volume_ratio'].diff()  # Aceleração do volume
            
            # === VOLATILIDADE AVANÇADA ===
            df['atr'] = self._calculate_atr(df, period=14)  # Average True Range
            df['volatility'] = df['close'].rolling(window=10, min_periods=1).std()
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20, min_periods=1).mean()
            
            # === PRESSÃO DE MERCADO MELHORADA ===
            df['body_size'] = abs(df['close'] - df['open']) / df['open']
            df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / df['open']
            df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / df['open']
            
            # Pressão baseada em candles
            df['bullish_candle'] = (df['close'] > df['open']).astype(int)
            df['bearish_candle'] = (df['close'] < df['open']).astype(int)
            df['doji_candle'] = (abs(df['close'] - df['open']) / df['open'] < 0.001).astype(int)
            
            # Pressão acumulada
            df['buying_pressure'] = np.where(df['close'] > df['open'], 
                                           df['tick_volume'] * df['body_size'], 0)
            df['selling_pressure'] = np.where(df['close'] < df['open'], 
                                            df['tick_volume'] * df['body_size'], 0)
            df['net_pressure'] = (df['buying_pressure'] - df['selling_pressure']).rolling(window=5).sum()
            df['pressure_momentum'] = df['net_pressure'].diff()  # Momentum da pressão
            
            # === MOMENTUM AVANÇADO ===
            df['roc'] = df['close'].pct_change(periods=5) * 100
            df['momentum'] = df['close'] / df['close'].shift(10) - 1
            df['price_acceleration'] = df['close'].diff().diff()  # Segunda derivada do preço
            
            # === TENDÊNCIA MULTI-TIMEFRAME ===
            df['trend_short'] = np.where(df['ema_8'] > df['ema_21'], 1, -1)
            df['trend_medium'] = np.where(df['sma_5'] > df['sma_10'], 1, -1)
            df['trend_long'] = np.where(df['sma_10'] > df['sma_20'], 1, -1)
            df['trend_consensus'] = df['trend_short'] + df['trend_medium'] + df['trend_long']
            df['trend_strength'] = abs(df['ema_8'] - df['ema_21']) / df['ema_21'] * 100
            
            return df.fillna(0)
            
        except Exception as e:
            logging.error(f"Erro ao preparar features: {e}")
            return None
    
    def _calculate_atr(self, df, period=14):
        """Calcula Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(window=period, min_periods=1).mean()

    def advanced_strategy_v2(self, df):
        """Estratégia otimizada v2.0 com melhorias implementadas"""
        try:
            features = self.prepare_features_optimized(df)
            if features is None or len(features) == 0:
                return None, 0, {}, "LOW"
            
            last_row = features.iloc[-1]
            prev_row = features.iloc[-2] if len(features) > 1 else last_row
            
            # Sistema de pontuação adaptativo
            base_score = 50
            score = base_score
            signals = {}
            confidence_factors = []
            
            # === 1. ANÁLISE DE TENDÊNCIA (30%) ===
            trend_score = 0
            
            # Consenso de tendência (peso maior)
            trend_consensus = last_row['trend_consensus']
            if trend_consensus >= 2:  # 2 ou 3 indicadores concordam
                trend_score += 12
                signals['trend_consensus'] = f"Forte Alta ({trend_consensus}/3)"
                confidence_factors.append("TREND_STRONG_UP")
            elif trend_consensus <= -2:
                trend_score -= 12
                signals['trend_consensus'] = f"Forte Baixa ({trend_consensus}/3)"
                confidence_factors.append("TREND_STRONG_DOWN")
            elif trend_consensus == 1:
                trend_score += 6
                signals['trend_consensus'] = f"Moderada Alta ({trend_consensus}/3)"
            elif trend_consensus == -1:
                trend_score -= 6
                signals['trend_consensus'] = f"Moderada Baixa ({trend_consensus}/3)"
            else:
                signals['trend_consensus'] = "Indefinida (0/3)"
            
            # Força da tendência
            if last_row['trend_strength'] > 0.5:
                trend_bonus = min(6, last_row['trend_strength'] * 2)
                trend_score += trend_bonus if trend_consensus > 0 else -trend_bonus
                signals['trend_strength'] = f"Forte ({last_row['trend_strength']:.2f}%)"
                confidence_factors.append("TREND_STRENGTH")
            else:
                signals['trend_strength'] = f"Fraca ({last_row['trend_strength']:.2f}%)"
            
            score += trend_score
            
            # === 2. ANÁLISE DE MOMENTUM (25%) ===
            momentum_score = 0
            
            # RSI otimizado com zonas específicas
            rsi = last_row['rsi_smooth']
            if rsi < 25:  # Oversold extremo
                momentum_score += 10
                signals['rsi'] = f"Oversold Extremo ({rsi:.1f})"
                confidence_factors.append("RSI_OVERSOLD")
            elif 25 <= rsi <= 35:  # Oversold recovery
                momentum_score += 6
                signals['rsi'] = f"Recovery ({rsi:.1f})"
            elif 65 <= rsi <= 75:  # Overbought entry
                momentum_score -= 6
                signals['rsi'] = f"Overbought ({rsi:.1f})"
            elif rsi > 75:  # Overbought extremo
                momentum_score -= 10
                signals['rsi'] = f"Overbought Extremo ({rsi:.1f})"
                confidence_factors.append("RSI_OVERBOUGHT")
            else:
                signals['rsi'] = f"Neutro ({rsi:.1f})"
            
            # MACD com momentum
            if (last_row['macd'] > last_row['macd_signal'] and 
                last_row['macd_momentum'] > 0):
                momentum_score += 8
                signals['macd'] = f"Bullish + Momentum ({last_row['macd']:.5f})"
                confidence_factors.append("MACD_BULLISH_MOMENTUM")
            elif (last_row['macd'] < last_row['macd_signal'] and 
                  last_row['macd_momentum'] < 0):
                momentum_score -= 8
                signals['macd'] = f"Bearish + Momentum ({last_row['macd']:.5f})"
                confidence_factors.append("MACD_BEARISH_MOMENTUM")
            else:
                signals['macd'] = f"Neutro ({last_row['macd']:.5f})"
            
            score += momentum_score
            
            # === 3. ANÁLISE DE VOLUME (20%) ===
            volume_score = 0
            
            # Volume com confirmação
            if last_row['volume_ratio'] > 1.5 and last_row['volume_acceleration'] > 0:
                volume_score += 10
                signals['volume'] = f"Alto + Aceleração ({last_row['volume_ratio']:.2f}x)"
                confidence_factors.append("VOLUME_SURGE")
            elif last_row['volume_ratio'] > 1.2:
                volume_score += 6
                signals['volume'] = f"Alto ({last_row['volume_ratio']:.2f}x)"
            elif last_row['volume_ratio'] < 0.6:
                volume_score -= 4
                signals['volume'] = f"Baixo ({last_row['volume_ratio']:.2f}x)"
            else:
                signals['volume'] = f"Normal ({last_row['volume_ratio']:.2f}x)"
            
            # Penalizar volume muito baixo
            if last_row['volume_ratio'] < 0.5:
                score *= 0.85  # Reduz score em 15%
                signals['volume_penalty'] = "Volume insuficiente"
            
            score += volume_score
            
            # === 4. ANÁLISE DE PRESSÃO (15%) ===
            pressure_score = 0
            
            # Pressão com momentum
            if (last_row['net_pressure'] > 2000 and 
                last_row['pressure_momentum'] > 0):
                pressure_score += 8
                signals['pressure'] = f"Forte Compra + Momentum ({last_row['net_pressure']:.0f})"
                confidence_factors.append("PRESSURE_BUYING_SURGE")
            elif (last_row['net_pressure'] < -2000 and 
                  last_row['pressure_momentum'] < 0):
                pressure_score -= 8
                signals['pressure'] = f"Forte Venda + Momentum ({last_row['net_pressure']:.0f})"
                confidence_factors.append("PRESSURE_SELLING_SURGE")
            elif last_row['net_pressure'] > 500:
                pressure_score += 4
                signals['pressure'] = f"Compra ({last_row['net_pressure']:.0f})"
            elif last_row['net_pressure'] < -500:
                pressure_score -= 4
                signals['pressure'] = f"Venda ({last_row['net_pressure']:.0f})"
            else:
                signals['pressure'] = f"Equilibrada ({last_row['net_pressure']:.0f})"
            
            score += pressure_score
            
            # === 5. ANÁLISE DE VOLATILIDADE (10%) ===
            volatility_score = 0
            
            # Volatilidade ideal para trading
            if 0.8 <= last_row['volatility_ratio'] <= 1.8:
                volatility_score += 5
                signals['volatility'] = f"Ideal ({last_row['volatility_ratio']:.2f}x)"
                confidence_factors.append("VOLATILITY_IDEAL")
            elif last_row['volatility_ratio'] > 3:
                volatility_score -= 5
                signals['volatility'] = f"Muito Alta ({last_row['volatility_ratio']:.2f}x)"
            else:
                signals['volatility'] = f"Normal ({last_row['volatility_ratio']:.2f}x)"
            
            # Bollinger Bands
            bb_pos = last_row['bb_position']
            if bb_pos < 0.15:  # Próximo da banda inferior
                volatility_score += 3
                signals['bollinger'] = f"Oversold ({bb_pos:.2f})"
            elif bb_pos > 0.85:  # Próximo da banda superior
                volatility_score -= 3
                signals['bollinger'] = f"Overbought ({bb_pos:.2f})"
            else:
                signals['bollinger'] = f"Meio ({bb_pos:.2f})"
            
            score += volatility_score
            
            # === DETERMINAÇÃO FINAL COM CONFIANÇA ADAPTATIVA ===
            
            # Calcular nível de confiança
            high_confidence_count = len([f for f in confidence_factors if f in [
                'TREND_STRONG_UP', 'TREND_STRONG_DOWN', 'RSI_OVERSOLD', 'RSI_OVERBOUGHT',
                'MACD_BULLISH_MOMENTUM', 'MACD_BEARISH_MOMENTUM', 'VOLUME_SURGE', 
                'PRESSURE_BUYING_SURGE', 'PRESSURE_SELLING_SURGE', 'VOLATILITY_IDEAL'
            ]])
            
            if high_confidence_count >= 3:
                confidence_level = "MUITO_ALTA"
                threshold_buy = 51
                threshold_sell = 49
            elif high_confidence_count >= 2:
                confidence_level = "ALTA"
                threshold_buy = 52
                threshold_sell = 48
            elif high_confidence_count >= 1:
                confidence_level = "MEDIA"
                threshold_buy = 53
                threshold_sell = 47
            else:
                confidence_level = "BAIXA"
                threshold_buy = 55
                threshold_sell = 45
            
            # Determinar direção
            if score > threshold_buy:
                direction = '🟩COMPRA'
            elif score < threshold_sell:
                direction = '🟥VENDA'
            else:
                direction = None
            
            return direction, score, signals, confidence_level

        except Exception as e:
            logging.error(f"Erro na estratégia otimizada: {e}")
            return None, 0, {}, "LOW"

    def wait_for_minute_turn_30s(self):
        """Aguarda a virada do minuto para estratégia de 30 segundos"""
        now = datetime.now()
        current_second = now.second

        # Se já passou dos 25 segundos, aguarda o próximo minuto
        if current_second > 25:
            next_minute = (now + timedelta(minutes=1)).replace(second=0, microsecond=0)
            sleep_time = (next_minute - now).total_seconds()
            logging.info(f"⏰ Aguardando próximo minuto: {sleep_time:.1f}s")
            time.sleep(sleep_time)
            return True

        # Se está antes dos 25 segundos, aguarda até os 25 segundos
        elif current_second < 25:
            target_time = now.replace(second=25, microsecond=0)
            sleep_time = (target_time - now).total_seconds()
            if sleep_time > 0:
                logging.info(f"⏰ Aguardando até 25s do minuto: {sleep_time:.1f}s")
                time.sleep(sleep_time)
            return True

        # Se está exatamente nos 25 segundos, pode prosseguir
        return True

    def gerar_sinal_30s(self):
        """Gera sinal para estratégia de 30 segundos na Binomo"""
        # Verifica trade ativo
        if self.active_trade and datetime.now() < self.active_trade.expiration_time:
            remaining_time = (self.active_trade.expiration_time - datetime.now()).total_seconds()
            return f"⏳ Trade ativo! Aguarde {remaining_time:.0f} segundos para o resultado."

        # Calcula resultado se trade expirado
        if self.active_trade and datetime.now() >= self.active_trade.expiration_time:
            self._calculate_and_send_result_optimized()
            self.active_trade = None

        if self.processing_signal:
            return "⚠️ Processando sinal..."

        self.processing_signal = True
        try:
            # Obter dados em tempo real para análise
            df = self.data_provider.get_historical_data(count=25)
            if df is None or len(df) < 20:
                self.processing_signal = False
                return "⚠️ Dados insuficientes para análise."

            # Executar estratégia otimizada
            direction, score, signals, confidence_level = self.advanced_strategy_v2(df)

            if direction is None:
                self.processing_signal = False
                return f"⚪ Sinal neutro (Score: {score:.2f}, Confiança: {confidence_level})"

            # Aguardar momento ideal para entrada (25 segundos do minuto)
            self.wait_for_minute_turn_30s()

            # Verificar se ainda temos tempo para entrar (máximo 29 segundos)
            current_second = datetime.now().second
            if current_second > 29:
                self.processing_signal = False
                return "⚠️ Tempo limite excedido para entrada (>29s)"

            # Obter preço de entrada no momento exato
            entry_price = self.data_provider.get_current_price()
            if entry_price is None:
                self.processing_signal = False
                return "⚠️ Erro ao obter preço de entrada."

            # Criar trade para 30 segundos
            entry_time = datetime.now()
            expiration_time = entry_time + timedelta(seconds=30)
            self.active_trade = TradeResult(
                entry_time, entry_price, direction, score,
                confidence_level, expiration_time=expiration_time
            )

            # Preparar mensagem para estratégia de 30s
            confidence_emoji = {
                "MUITO_ALTA": "🔥",
                "ALTA": "⭐",
                "MEDIA": "📊",
                "BAIXA": "⚠️"
            }

            message = (
                f"🎯 **SINAL 30 SEGUNDOS - BINOMO**\n"
                f"{direction} | {confidence_emoji.get(confidence_level, '📊')} {confidence_level}\n"
                f"📈 Ativo: {self.config.get('SYMBOL', 'CRY/IDX')}\n"
                f"⏳ Entrada: {entry_time.strftime('%H:%M:%S')} (25s do minuto)\n"
                f"⏰ Expiração: {expiration_time.strftime('%H:%M:%S')} (30s)\n\n"
                f"📊 Score: {score:.2f}/100\n"
                f"💰 Preço: {entry_price:.5f}\n\n"
                f"🔍 **ANÁLISE DETALHADA**\n"
            )

            # Adicionar indicadores principais
            key_indicators = ['trend_consensus', 'rsi', 'macd', 'volume', 'pressure', 'volatility']
            for indicator in key_indicators:
                if indicator in signals:
                    message += f"• {indicator.upper()}: {signals[indicator]}\n"

            # Adicionar estatísticas de performance
            if self.performance_metrics['total_signals'] > 0:
                win_rate = self.performance_metrics['win_rate']
                message += f"\n📈 Win Rate: {win_rate:.1f}% ({self.performance_metrics['wins']}/{self.performance_metrics['total_signals']})"

            # Enviar sinal
            self.bot.send_message(self.config['chat_id'], message)
            logging.info(f"Sinal 30s enviado: {direction} - Score: {score:.2f} - Confiança: {confidence_level} - Entrada: {entry_time.strftime('%H:%M:%S')}")

            # Registrar no histórico
            self.signal_history.append({
                'timestamp': entry_time,
                'direction': direction,
                'score': score,
                'confidence': confidence_level,
                'entry_price': entry_price,
                'expiration_seconds': 30
            })

            self.processing_signal = False
            return f"✅ Sinal 30s {direction} enviado! Entrada: {entry_time.strftime('%H:%M:%S')} (Confiança: {confidence_level})"

        except Exception as e:
            logging.error(f"Erro ao gerar sinal 30s: {e}")
            self.processing_signal = False
            return "⚠️ Erro ao gerar sinal."

    def gerar_sinal_otimizado(self):
        """Mantém compatibilidade - redireciona para estratégia de 30s"""
        return self.gerar_sinal_30s()

    def _calculate_and_send_result_optimized(self):
        """Calcula resultado para estratégia de 30 segundos"""
        if not self.active_trade:
            return

        try:
            exit_price = self.data_provider.get_current_price()
            if exit_price is None:
                logging.error("Erro ao obter preço de fechamento")
                return

            # Calcular resultado
            if self.active_trade.operation == '🟩COMPRA':
                is_win = exit_price > self.active_trade.entry_price
            else:
                is_win = exit_price < self.active_trade.entry_price

            result = "✅GAIN 📈" if is_win else "❌LOSS 📉"
            price_change = ((exit_price - self.active_trade.entry_price) / self.active_trade.entry_price) * 100

            # Atualizar métricas de performance
            self.performance_metrics['total_signals'] += 1
            if is_win:
                self.performance_metrics['wins'] += 1
            else:
                self.performance_metrics['losses'] += 1

            self.performance_metrics['win_rate'] = (
                self.performance_metrics['wins'] / self.performance_metrics['total_signals'] * 100
            )

            # Mensagem de resultado para 30s
            confidence_emoji = {
                "MUITO_ALTA": "🔥",
                "ALTA": "⭐",
                "MEDIA": "📊",
                "BAIXA": "⚠️"
            }

            duration = (datetime.now() - self.active_trade.entry_time).total_seconds()

            result_message = (
                f"🔄 **RESULTADO - 30 SEGUNDOS**\n"
                f"Operação: {self.active_trade.operation} {confidence_emoji.get(self.active_trade.confidence_level, '📊')}\n"
                f"Confiança: {self.active_trade.confidence_level}\n"
                f"Score: {self.active_trade.probability:.2f}/100\n\n"
                f"⏰ Entrada: {self.active_trade.entry_time.strftime('%H:%M:%S')}\n"
                f"⏰ Saída: {datetime.now().strftime('%H:%M:%S')} ({duration:.0f}s)\n"
                f"💰 Preço Entrada: {self.active_trade.entry_price:.5f}\n"
                f"💰 Preço Saída: {exit_price:.5f}\n"
                f"📊 Variação: {price_change:+.3f}%\n"
                f"🎯 Resultado: {result}\n\n"
                f"📈 **PERFORMANCE GERAL**\n"
                f"Win Rate: {self.performance_metrics['win_rate']:.1f}%\n"
                f"Wins: {self.performance_metrics['wins']} | Losses: {self.performance_metrics['losses']}\n"
                f"Total: {self.performance_metrics['total_signals']} sinais"
            )

            self.bot.send_message(self.config['chat_id'], result_message)
            logging.info(f"Resultado 30s: {result} - Variação: {price_change:+.3f}% - Duração: {duration:.0f}s - Win Rate: {self.performance_metrics['win_rate']:.1f}%")

        except Exception as e:
            logging.error(f"Erro ao calcular resultado: {e}")

    def _setup_telegram_handlers(self):
        """Configura comandos otimizados do Telegram"""
        @self.bot.message_handler(commands=['start'])
        def start(message):
            welcome_msg = (
                "🚀 **Bot Binomo - Estratégia 30 SEGUNDOS**\n\n"
                "Comandos disponíveis:\n"
                "• /sinal - Gerar sinal de 30 segundos\n"
                "• /stats - Ver estatísticas de performance\n"
                "• /config - Ver configurações atuais\n\n"
                "⚡ **ESTRATÉGIA 30 SEGUNDOS:**\n"
                "• Aguarda virada do minuto\n"
                "• Entrada aos 25 segundos\n"
                "• Expiração em 30 segundos\n"
                "• Máximo 29s para clicar\n"
                "• Análise multi-indicador avançada"
            )
            self.bot.send_message(message.chat.id, welcome_msg)

        @self.bot.message_handler(commands=['sinal'])
        def send_signal(message):
            sinal = self.gerar_sinal_30s()
            self.bot.send_message(message.chat.id, sinal)

        @self.bot.message_handler(commands=['stats'])
        def show_stats(message):
            if self.performance_metrics['total_signals'] == 0:
                stats_msg = "📊 Nenhum sinal gerado ainda."
            else:
                stats_msg = (
                    f"📊 **ESTATÍSTICAS DE PERFORMANCE**\n\n"
                    f"🎯 Win Rate: {self.performance_metrics['win_rate']:.1f}%\n"
                    f"✅ Wins: {self.performance_metrics['wins']}\n"
                    f"❌ Losses: {self.performance_metrics['losses']}\n"
                    f"📈 Total de Sinais: {self.performance_metrics['total_signals']}\n\n"
                    f"📋 Últimos 5 sinais:\n"
                )

                # Mostrar últimos 5 sinais
                recent_signals = self.signal_history[-5:] if len(self.signal_history) >= 5 else self.signal_history
                for i, signal in enumerate(recent_signals, 1):
                    stats_msg += f"{i}. {signal['direction']} - Score: {signal['score']:.1f} - {signal['confidence']}\n"

            self.bot.send_message(message.chat.id, stats_msg)

        @self.bot.message_handler(commands=['config'])
        def show_config(message):
            config_msg = (
                f"⚙️ **CONFIGURAÇÕES - ESTRATÉGIA 30S**\n\n"
                f"📈 Símbolo: {self.config.get('SYMBOL', 'N/A')}\n"
                f"⏱️ Expiração: 30 segundos\n"
                f"🎯 Entrada: 25s do minuto\n"
                f"⏰ Limite Clique: 29s\n"
                f"🎯 Confiança Mínima: {self.config.get('min_confidence', 'N/A')}\n"
                f"🔄 API Tempo Real: {'✅' if self.config.get('USE_REALTIME_API') else '❌'}\n"
                f"📊 Dados Conectados: {'✅' if self.data_provider.test_connection() else '❌'}"
            )
            self.bot.send_message(message.chat.id, config_msg)

    def run(self):
        """Executa o bot otimizado"""
        if not self.data_provider.test_connection():
            logging.error("Erro ao conectar com dados em tempo real")
            return

        # Enviar mensagem de inicialização
        startup_msg = (
            "🚀 **BOT BINOMO 30 SEGUNDOS ATIVO!**\n\n"
            "⚡ **ESTRATÉGIA 30 SEGUNDOS:**\n"
            "• Aguarda virada do minuto\n"
            "• Entrada precisa aos 25 segundos\n"
            "• Expiração em 30 segundos\n"
            "• Máximo 29s para executar\n"
            "• Análise multi-indicador avançada\n"
            "• Sistema de confiança adaptativo\n\n"
            "🎯 Use /sinal para começar!\n"
            "📊 Use /stats para ver performance\n"
            "⚙️ Use /config para ver configurações"
        )
        self.bot.send_message(self.config['chat_id'], startup_msg)

        logging.info("🚀 Bot Binomo 30 Segundos iniciado com dados em tempo real!")
        self.bot.polling()

if __name__ == "__main__":
    print("🚀 Iniciando Bot Binomo - Estratégia 30 Segundos...")
    print("⚡ Configuração:")
    print("   • Aguarda virada do minuto")
    print("   • Entrada aos 25 segundos")
    print("   • Expiração em 30 segundos")
    print("   • Máximo 29s para clicar")
    print("   • Análise multi-indicador")
    print()
    bot = OptimizedTradingBot()
    bot.run()
